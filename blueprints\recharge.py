from flask import Blueprint, request, jsonify, session, render_template
from models import *
from blueprints.auth import login_required
import datetime
from werkzeug.exceptions import BadRequest

recharge_bp = Blueprint('recharge', __name__)
bp = recharge_bp  # 为了与自动注册系统兼容

def get_single_recharge_detail(recharge_id, staff_role, staff_name, staff_area):
    """获取单个充值记录详情"""
    try:
        # 获取充值记录
        recharge_record = RechargeRecord.query.get(recharge_id)
        if not recharge_record:
            return jsonify({
                'success': False,
                'error': '充值记录不存在'
            }), 404
        
        # 检查权限
        if staff_role != 'manager':
            # 普通营业员只能查看自己操作的充值记录
            if recharge_record.operator != staff_name:
                return jsonify({
                    'success': False,
                    'error': '无权限查看此充值记录'
                }), 403
        elif staff_role == 'manager' and staff_area and staff_area != '总部':
            # 区域管理员只能查看自己区域的充值记录
            area_staff = Staff.query.filter_by(area=staff_area).all()
            area_staff_names = [user.name for user in area_staff]
            if recharge_record.operator not in area_staff_names:
                return jsonify({
                    'success': False,
                    'error': '无权限查看此充值记录'
                }), 403
        
        # 获取客户信息
        customer_name = '未知'
        customer_phone = '未知'
        current_balance = 0.0
        
        if recharge_record.customer_id:
            customer = Customer.query.get(recharge_record.customer_id)
            if customer:
                customer_name = customer.name
                customer_phone = customer.phone
                current_balance = (customer.balance or 0.0) + (customer.gift_balance or 0.0)
        
        return jsonify({
            'success': True,
            'id': recharge_record.id,
            'customer_name': customer_name,
            'customer_phone': customer_phone,
            'amount': float(recharge_record.amount),
            'gift_amount': float(recharge_record.gift_amount or 0),
            'payment_method': recharge_record.payment_method,
            'operator': recharge_record.operator or '未知',
            'created_at': recharge_record.created_at.isoformat(),
            'current_balance': current_balance,
            'remarks': recharge_record.remarks or ''
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'获取充值记录详情失败: {str(e)}'
        }), 500

@recharge_bp.route('/api/recharge_details', methods=['GET'])
@login_required
def get_recharge_details():
    """获取充值明细数据"""
    try:
        # 获取当前登录用户的角色和信息
        staff_role = session.get('staff_role', '')
        staff_name = session.get('staff_name', '')
        staff_area = session.get('staff_area', '')

        # 检查是否是获取单个充值记录详情
        recharge_id = request.args.get('recharge_id')
        if recharge_id:
            return get_single_recharge_detail(int(recharge_id), staff_role, staff_name, staff_area)

        # 获取查询参数
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        selected_operator = request.args.get('operator')  # 新增：营业员筛选参数
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)

        # 默认查询近7天数据
        if not start_date:
            start_date = (datetime.datetime.now() - datetime.timedelta(days=7)).strftime('%Y-%m-%d')
        if not end_date:
            end_date = datetime.datetime.now().strftime('%Y-%m-%d')

        # 转换为datetime对象
        start_datetime = datetime.datetime.strptime(f"{start_date} 00:00:00", '%Y-%m-%d %H:%M:%S')
        end_datetime = datetime.datetime.strptime(f"{end_date} 23:59:59", '%Y-%m-%d %H:%M:%S')

        # 构建充值记录查询
        recharge_query = RechargeRecord.query.filter(
            RechargeRecord.created_at.between(start_datetime, end_datetime)
        )

        # 应用营业员筛选（在权限过滤之前）
        if selected_operator:
            recharge_query = recharge_query.filter_by(operator=selected_operator)

        # 应用权限过滤
        if staff_role != 'manager':
            # 普通营业员只能查看自己操作的充值记录
            if not selected_operator:  # 如果没有指定营业员，默认查看自己的
                recharge_query = recharge_query.filter_by(operator=staff_name)
            elif selected_operator != staff_name:  # 如果指定了其他营业员，无权限查看
                recharge_query = recharge_query.filter(RechargeRecord.id == -1)  # 返回空结果
        elif staff_role == 'manager' and staff_area and staff_area != '总部':
            # 区域管理员只能查看自己区域的充值记录
            area_staff = Staff.query.filter_by(area=staff_area).all()
            area_staff_names = [user.name for user in area_staff]
            if not selected_operator:  # 如果没有指定营业员，查看区域内所有营业员
                recharge_query = recharge_query.filter(RechargeRecord.operator.in_(area_staff_names))
            elif selected_operator not in area_staff_names:  # 如果指定的营业员不在区域内，无权限查看
                recharge_query = recharge_query.filter(RechargeRecord.id == -1)  # 返回空结果

        # 按创建时间降序排序
        recharge_query = recharge_query.order_by(RechargeRecord.created_at.desc())

        # 分页
        paginated_records = recharge_query.paginate(
            page=page, per_page=per_page, error_out=False
        )

        # 构建结果数据
        recharge_details = []
        for record in paginated_records.items:
            # 获取客户信息
            customer_name = '未知'
            customer_phone = '未知'
            
            if record.customer_id:
                customer = Customer.query.get(record.customer_id)
                if customer:
                    customer_name = customer.name
                    customer_phone = customer.phone

            # 检查退充状态
            refund_status = '正常'
            total_refunded = 0.0

            # 查询该充值记录的所有退充记录
            refunds = RechargeRefund.query.filter_by(
                recharge_record_id=record.id,
                status='completed'
            ).all()

            if refunds:
                total_refunded = sum(refund.refund_amount for refund in refunds)
                if total_refunded >= record.amount:
                    refund_status = '已退'
                else:
                    refund_status = f'部分退充(¥{total_refunded:.2f})'

            recharge_details.append({
                'id': record.id,
                'operator': record.operator or '未知',
                'created_at': record.created_at.isoformat(),
                'customer_name': customer_name,
                'customer_phone': customer_phone,
                'amount': float(record.amount),
                'gift_amount': float(record.gift_amount or 0),
                'payment_method': record.payment_method,
                'remarks': record.remarks or '',
                'refund_status': refund_status,
                'total_refunded': total_refunded
            })

        return jsonify({
            'success': True,
            'recharge_details': recharge_details,
            'current_page': page,
            'total_pages': paginated_records.pages,
            'per_page': per_page,
            'total': paginated_records.total
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500


@recharge_bp.route('/api/export_recharge_details', methods=['GET'])
@login_required
def export_recharge_details():
    """导出充值明细Excel"""
    try:
        # 获取当前登录用户的角色和信息
        staff_role = session.get('staff_role', '')
        staff_name = session.get('staff_name', '')
        staff_area = session.get('staff_area', '')

        # 获取查询参数
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        selected_operator = request.args.get('operator')  # 新增：营业员筛选参数

        # 默认查询近7天数据
        if not start_date:
            start_date = (datetime.datetime.now() - datetime.timedelta(days=7)).strftime('%Y-%m-%d')
        if not end_date:
            end_date = datetime.datetime.now().strftime('%Y-%m-%d')

        # 转换为datetime对象
        start_datetime = datetime.datetime.strptime(f"{start_date} 00:00:00", '%Y-%m-%d %H:%M:%S')
        end_datetime = datetime.datetime.strptime(f"{end_date} 23:59:59", '%Y-%m-%d %H:%M:%S')

        # 构建充值记录查询
        recharge_query = RechargeRecord.query.filter(
            RechargeRecord.created_at.between(start_datetime, end_datetime)
        )

        # 应用营业员筛选（在权限过滤之前）
        if selected_operator:
            recharge_query = recharge_query.filter_by(operator=selected_operator)

        # 应用权限过滤
        if staff_role != 'manager':
            # 普通营业员只能查看自己操作的充值记录
            if not selected_operator:  # 如果没有指定营业员，默认查看自己的
                recharge_query = recharge_query.filter_by(operator=staff_name)
            elif selected_operator != staff_name:  # 如果指定了其他营业员，无权限查看
                recharge_query = recharge_query.filter(RechargeRecord.id == -1)  # 返回空结果
        elif staff_role == 'manager' and staff_area and staff_area != '总部':
            # 区域管理员只能查看自己区域的充值记录
            area_staff = Staff.query.filter_by(area=staff_area).all()
            area_staff_names = [user.name for user in area_staff]
            if not selected_operator:  # 如果没有指定营业员，查看区域内所有营业员
                recharge_query = recharge_query.filter(RechargeRecord.operator.in_(area_staff_names))
            elif selected_operator not in area_staff_names:  # 如果指定的营业员不在区域内，无权限查看
                recharge_query = recharge_query.filter(RechargeRecord.id == -1)  # 返回空结果

        # 按创建时间降序排序
        records = recharge_query.order_by(RechargeRecord.created_at.desc()).all()

        # 构建Excel数据
        import io
        from flask import Response
        
        # 简单的CSV格式（可以用Excel打开）
        output = io.StringIO()
        
        # 写入标题行
        headers = ['营业员姓名', '充值时间', '客户姓名', '客户电话', '充值金额', '赠送金额', '支付方式', '备注']
        output.write(','.join(headers) + '\n')
        
        # 写入数据行
        for record in records:
            # 获取客户信息
            customer_name = '未知'
            customer_phone = '未知'
            
            if record.customer_id:
                customer = Customer.query.get(record.customer_id)
                if customer:
                    customer_name = customer.name
                    customer_phone = customer.phone

            row = [
                record.operator or '未知',
                record.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                customer_name,
                customer_phone,
                str(record.amount),
                str(record.gift_amount or 0),
                record.payment_method,
                record.remarks or ''
            ]
            output.write(','.join(row) + '\n')
        
        # 创建响应
        csv_data = output.getvalue()
        output.close()
        
        response = Response(
            csv_data,
            mimetype='text/csv',
            headers={'Content-Disposition': f'attachment; filename=充值明细_{start_date}_{end_date}.csv'}
        )
        
        return response

    except Exception as e:
        return jsonify({'error': str(e)}), 500 

@recharge_bp.route('/api/recharge_gift_rules', methods=['GET'])
@login_required
def list_recharge_gift_rules():
    """获取充值赠送规则列表"""
    try:
        # 获取全部规则，按照最小充值金额升序排列
        rules = RechargeGiftRule.query.order_by(RechargeGiftRule.min_amount.asc()).all()
        result = []
        for r in rules:
            result.append({
                'id': r.id,
                'min_amount': float(r.min_amount),
                'gift_type': r.gift_type,
                'gift_value': float(r.gift_value),
                'is_active': r.is_active,
                'created_at': r.created_at.isoformat()
            })
        return jsonify({'success': True, 'rules': result})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@recharge_bp.route('/api/recharge_gift_rules', methods=['POST'])
@login_required
def create_recharge_gift_rule():
    """创建充值赠送规则"""
    try:
        data = request.json or {}
        min_amount = float(data.get('min_amount', 0))
        gift_type = data.get('gift_type')
        gift_value = float(data.get('gift_value', 0))
        is_active = bool(data.get('is_active', True))

        # 参数校验
        if min_amount <= 0:
            return jsonify({'error': '最小充值金额必须大于0'}), 400
        if gift_type not in ['percentage', 'fixed']:
            return jsonify({'error': '无效的赠送类型'}), 400
        if gift_value <= 0:
            return jsonify({'error': '赠送值必须大于0'}), 400
        if gift_type == 'percentage' and gift_value > 100:
            return jsonify({'error': '百分比赠送不能超过100'}), 400

        rule = RechargeGiftRule(
            min_amount=min_amount,
            gift_type=gift_type,
            gift_value=gift_value,
            is_active=is_active
        )
        db.session.add(rule)
        db.session.commit()

        return jsonify({'success': True, 'rule': {
            'id': rule.id,
            'min_amount': rule.min_amount,
            'gift_type': rule.gift_type,
            'gift_value': rule.gift_value,
            'is_active': rule.is_active,
            'created_at': rule.created_at.isoformat()
        }})
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@recharge_bp.route('/api/recharge_gift_rules/<int:rule_id>', methods=['PUT'])
@login_required
def update_recharge_gift_rule(rule_id):
    """更新充值赠送规则"""
    try:
        rule = RechargeGiftRule.query.get_or_404(rule_id)
        data = request.json or {}

        if 'min_amount' in data:
            min_amount = float(data['min_amount'])
            if min_amount <= 0:
                return jsonify({'error': '最小充值金额必须大于0'}), 400
            rule.min_amount = min_amount
        if 'gift_type' in data:
            if data['gift_type'] not in ['percentage', 'fixed']:
                return jsonify({'error': '无效的赠送类型'}), 400
            rule.gift_type = data['gift_type']
        if 'gift_value' in data:
            gift_value = float(data['gift_value'])
            if gift_value <= 0:
                return jsonify({'error': '赠送值必须大于0'}), 400
            if rule.gift_type == 'percentage' and gift_value > 100:
                return jsonify({'error': '百分比赠送不能超过100'}), 400
            rule.gift_value = gift_value
        if 'is_active' in data:
            rule.is_active = bool(data['is_active'])

        db.session.commit()

        return jsonify({'success': True})
    except BadRequest as br:
        return jsonify({'error': str(br)}), 400
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@recharge_bp.route('/api/recharge_gift_rules/<int:rule_id>', methods=['DELETE'])
@login_required
def delete_recharge_gift_rule(rule_id):
    """删除充值赠送规则"""
    try:
        rule = RechargeGiftRule.query.get_or_404(rule_id)
        db.session.delete(rule)
        db.session.commit()
        return jsonify({'success': True})
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@recharge_bp.route('/api/calculate_gift', methods=['POST'])
@login_required
def calculate_gift():
    """根据充值金额计算赠送金额（返回最优赠送）"""
    try:
        data = request.get_json() or {}
        amount = float(data.get('amount', 0))
        if amount <= 0:
            return jsonify({'gift_amount': 0.0})

        from utils import calculate_gift_amount
        gift_amount = calculate_gift_amount(amount)
        return jsonify({'gift_amount': round(float(gift_amount), 2)})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@recharge_bp.route('/api/calculate_gift_options', methods=['POST'])
@login_required
def calculate_gift_options():
    """根据充值金额返回可选赠送规则列表及推荐规则"""
    try:
        data = request.get_json() or {}
        amount = float(data.get('amount', 0))
        if amount <= 0:
            return jsonify({'options': []})

        # 查询可用规则，且满足金额条件
        rules = RechargeGiftRule.query.filter(
            RechargeGiftRule.is_active == True,
            RechargeGiftRule.min_amount <= amount
        ).order_by(RechargeGiftRule.min_amount.asc()).all()

        options = []
        max_gift = -1
        recommended = None

        for rule in rules:
            # 计算该规则可赠送金额
            if rule.gift_type == 'percentage':
                gift_amount = amount * (rule.gift_value / 100.0)
                desc_value = f"{rule.gift_value}%"
            else:
                gift_amount = rule.gift_value
                desc_value = f"¥{rule.gift_value}"

            option = {
                'rule_id': rule.id,
                'gift_amount': round(float(gift_amount), 2),
                'description': f"充值满¥{rule.min_amount}赠送{desc_value}",
            }
            options.append(option)

            if gift_amount > max_gift:
                max_gift = gift_amount
                recommended = option

        return jsonify({'options': options, 'recommended': recommended})
    except Exception as e:
        return jsonify({'error': str(e)}), 500 

# ========================================================================
# 充值退充相关API接口
# ========================================================================

@recharge_bp.route('/api/recharge/<int:recharge_id>/refundable', methods=['GET'])
@login_required
def get_refundable_info(recharge_id):
    """获取指定充值记录的可退充信息"""
    try:
        from utils import calculate_refundable_amount, validate_refund_operation
        
        staff_name = session.get('staff_name', '')
        
        # 检查基本权限（只检查充值记录是否存在和区域权限）
        recharge_record = RechargeRecord.query.get(recharge_id)
        if not recharge_record:
            return jsonify({
                'success': False,
                'error': '充值记录不存在'
            }), 404
        
        # 检查区域权限
        from utils import check_area_permission
        area_check = check_area_permission(staff_name, recharge_record.customer_id)
        if not area_check['has_permission']:
            return jsonify({
                'success': False,
                'error': area_check['message']
            }), 403
        
        # 获取可退充信息
        refund_info = calculate_refundable_amount(recharge_id)
        
        return jsonify({
            'success': True,
            'refundable_amount': refund_info['refundable_amount'],
            'gift_amount_to_deduct': refund_info['gift_amount_to_deduct'],
            'current_balance': refund_info['current_balance'],
            'current_gift_balance': refund_info['current_gift_balance'],
            'usage_details': refund_info['usage_details'],
            'can_refund': refund_info['can_refund'],
            'reason': refund_info.get('reason', '')
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'获取可退充信息失败: {str(e)}'
        }), 500

@recharge_bp.route('/api/recharge/<int:recharge_id>/refund', methods=['POST'])
@login_required
def refund_recharge(recharge_id):
    """
    处理充值退款
    """
    data = request.get_json()
    if not data or 'refund_amount' not in data or 'refund_reason' not in data:
        return jsonify({'success': False, 'message': '退款金额和原因不能为空'}), 400

    try:
        recharge = RechargeRecord.query.get_or_404(recharge_id)

        # 检查是否已经退充过
        existing_refunds = RechargeRefund.query.filter_by(
            recharge_record_id=recharge_id,
            status='completed'
        ).all()

        if existing_refunds:
            total_refunded = sum(refund.refund_amount for refund in existing_refunds)
            if total_refunded >= recharge.amount:
                return jsonify({'success': False, 'message': '该充值记录已经全额退款'}), 400

        # 获取退款金额和原因
        refund_amount = float(data['refund_amount'])
        refund_reason = data['refund_reason']
        staff_name = session.get('staff_name', '')

        # 使用工具函数执行退充操作
        from utils import execute_recharge_refund
        result = execute_recharge_refund(
            recharge_id,
            refund_amount,
            refund_reason,
            staff_name
        )

        if result['success']:
            return jsonify({
                'success': True,
                'refund_id': result['refund_id'],
                'message': result['message'],
                'new_balance': result['balance_after_refund'],
                'new_gift_balance': result['gift_balance_after_refund']
            })
        else:
            return jsonify({
                'success': False,
                'message': result['message']
            }), 400

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'退充操作失败: {str(e)}'
        }), 500

@recharge_bp.route('/api/recharge/refunds', methods=['GET'])
@login_required
def get_refund_records():
    """获取退充记录列表"""
    try:
        staff_role = session.get('staff_role', '')
        staff_name = session.get('staff_name', '')
        staff_area = session.get('staff_area', '')
        
        # 获取查询参数
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        customer_phone = request.args.get('customer_phone')
        operator = request.args.get('operator')
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        
        # 默认查询近7天数据
        if not start_date:
            start_date = (datetime.datetime.now() - datetime.timedelta(days=7)).strftime('%Y-%m-%d')
        if not end_date:
            end_date = datetime.datetime.now().strftime('%Y-%m-%d')
        
        # 转换为datetime对象
        start_datetime = datetime.datetime.strptime(f"{start_date} 00:00:00", '%Y-%m-%d %H:%M:%S')
        end_datetime = datetime.datetime.strptime(f"{end_date} 23:59:59", '%Y-%m-%d %H:%M:%S')
        
        # 构建查询
        refund_query = RechargeRefund.query.filter(
            RechargeRefund.created_at.between(start_datetime, end_datetime)
        )
        
        # 应用筛选条件
        if operator:
            refund_query = refund_query.filter_by(operator=operator)
        
        if customer_phone:
            # 通过客户手机号筛选
            customer = Customer.query.filter_by(phone=customer_phone).first()
            if customer:
                refund_query = refund_query.filter_by(customer_id=customer.id)
            else:
                # 如果客户不存在，返回空结果
                refund_query = refund_query.filter_by(id=-1)
        
        # 应用权限过滤
        if staff_role != 'manager':
            # 普通营业员只能查看自己操作的退充记录
            refund_query = refund_query.filter_by(operator=staff_name)
        elif staff_role == 'manager' and staff_area and staff_area != '总部':
            # 区域管理员只能查看自己区域的退充记录
            area_staff = Staff.query.filter_by(area=staff_area).all()
            area_staff_names = [user.name for user in area_staff]
            refund_query = refund_query.filter(RechargeRefund.operator.in_(area_staff_names))
        
        # 按创建时间降序排序
        refund_query = refund_query.order_by(RechargeRefund.created_at.desc())
        
        # 分页
        paginated_refunds = refund_query.paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        # 构建结果数据
        refund_details = []
        for refund in paginated_refunds.items:
            # 获取客户信息
            customer = Customer.query.get(refund.customer_id)
            customer_name = customer.name if customer else '未知'
            customer_phone = customer.phone if customer else '未知'
            
            # 获取原充值记录信息
            original_recharge = RechargeRecord.query.get(refund.recharge_record_id)
            original_amount = original_recharge.amount if original_recharge else 0.0
            original_gift_amount = original_recharge.gift_amount if original_recharge else 0.0
            
            refund_details.append({
                'id': refund.id,
                'original_recharge_id': refund.recharge_record_id,
                'customer_name': customer_name,
                'customer_phone': customer_phone,
                'refund_amount': float(refund.refund_amount),
                'refund_gift_amount': 0.0,  # 现有数据库结构中没有此字段
                'refund_reason': refund.refund_reason or '',
                'operator': refund.operator,
                'approved_by': refund.approved_by or '',
                'status': refund.status,
                'created_at': refund.created_at.isoformat(),
                'original_recharge': {
                    'amount': float(original_amount),
                    'gift_amount': float(original_gift_amount),
                    'created_at': original_recharge.created_at.isoformat() if original_recharge else None
                }
            })
        
        return jsonify({
            'success': True,
            'refunds': refund_details,
            'current_page': page,
            'total_pages': paginated_refunds.pages,
            'per_page': per_page,
            'total': paginated_refunds.total
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'获取退充记录失败: {str(e)}'
        }), 500

@recharge_bp.route('/api/recharge/refunds/export', methods=['GET'])
@login_required
def export_refund_records():
    """导出退充记录Excel"""
    try:
        staff_role = session.get('staff_role', '')
        staff_name = session.get('staff_name', '')
        staff_area = session.get('staff_area', '')
        
        # 获取查询参数
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        customer_phone = request.args.get('customer_phone')
        operator = request.args.get('operator')
        
        # 默认查询近7天数据
        if not start_date:
            start_date = (datetime.datetime.now() - datetime.timedelta(days=7)).strftime('%Y-%m-%d')
        if not end_date:
            end_date = datetime.datetime.now().strftime('%Y-%m-%d')
        
        # 转换为datetime对象
        start_datetime = datetime.datetime.strptime(f"{start_date} 00:00:00", '%Y-%m-%d %H:%M:%S')
        end_datetime = datetime.datetime.strptime(f"{end_date} 23:59:59", '%Y-%m-%d %H:%M:%S')
        
        # 构建查询（与列表查询逻辑相同）
        refund_query = RechargeRefund.query.filter(
            RechargeRefund.created_at.between(start_datetime, end_datetime)
        )
        
        if operator:
            refund_query = refund_query.filter_by(operator=operator)
        
        if customer_phone:
            customer = Customer.query.filter_by(phone=customer_phone).first()
            if customer:
                refund_query = refund_query.filter_by(customer_id=customer.id)
            else:
                refund_query = refund_query.filter_by(id=-1)
        
        # 应用权限过滤
        if staff_role != 'manager':
            refund_query = refund_query.filter_by(operator=staff_name)
        elif staff_role == 'manager' and staff_area and staff_area != '总部':
            area_staff = Staff.query.filter_by(area=staff_area).all()
            area_staff_names = [user.name for user in area_staff]
            refund_query = refund_query.filter(RechargeRefund.operator.in_(area_staff_names))
        
        # 按创建时间降序排序
        refunds = refund_query.order_by(RechargeRefund.created_at.desc()).all()
        
        # 构建CSV数据
        import io
        from flask import Response
        
        output = io.StringIO()
        
        # 写入标题行
        headers = ['退充ID', '原充值ID', '客户姓名', '客户电话', '退充金额', '扣除赠送金额', 
                  '退充原因', '操作员', '审批人', '状态', '退充时间', '原充值金额', '原赠送金额']
        output.write(','.join(headers) + '\n')
        
        # 写入数据行
        for refund in refunds:
            customer = Customer.query.get(refund.customer_id)
            customer_name = customer.name if customer else '未知'
            customer_phone = customer.phone if customer else '未知'
            
            original_recharge = RechargeRecord.query.get(refund.recharge_record_id)
            original_amount = original_recharge.amount if original_recharge else 0.0
            original_gift_amount = original_recharge.gift_amount if original_recharge else 0.0
            
            row = [
                str(refund.id),
                str(refund.recharge_record_id),
                customer_name,
                customer_phone,
                str(refund.refund_amount),
                '0.0',  # 现有数据库结构中没有refund_gift_amount字段
                refund.refund_reason or '',
                refund.operator,
                refund.approved_by or '',
                refund.status,
                refund.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                str(original_amount),
                str(original_gift_amount)
            ]
            output.write(','.join(row) + '\n')
        
        # 创建响应
        csv_data = output.getvalue()
        output.close()
        
        response = Response(
            csv_data,
            mimetype='text/csv',
            headers={'Content-Disposition': f'attachment; filename=退充记录_{start_date}_{end_date}.csv'}
        )
        
        return response
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'导出退充记录失败: {str(e)}'
        }), 500

# ========================================================================
# 小票补打印相关API接口
# ========================================================================

@recharge_bp.route('/recharge_receipt_reprint')
@login_required
def recharge_receipt_reprint_page():
    """充值小票补打印页面（网页打印模式）"""
    try:
        recharge_id = request.args.get('recharge_id', type=int)
        reprint_reason = request.args.get('reason', '客户要求补打印')
        
        if not recharge_id:
            return "缺少充值记录ID参数", 400
        
        # 获取充值记录详情
        staff_role = session.get('staff_role', '')
        staff_name = session.get('staff_name', '')
        staff_area = session.get('staff_area', '')
        
        recharge_detail = get_single_recharge_detail(recharge_id, staff_role, staff_name, staff_area)
        
        # 如果返回的是错误响应，直接返回
        if hasattr(recharge_detail, 'status_code'):
            return recharge_detail
        
        # 获取JSON数据
        recharge_data = recharge_detail.get_json()
        
        if not recharge_data.get('success'):
            return f"获取充值记录失败: {recharge_data.get('error', '未知错误')}", 400
        
        # 准备模板数据
        template_data = {
            'customer_name': recharge_data.get('customer_name', '未知'),
            'customer_phone': recharge_data.get('customer_phone', '未知'),
            'amount': recharge_data.get('amount', 0),
            'gift_amount': recharge_data.get('gift_amount', 0),
            'payment_method': recharge_data.get('payment_method', '未知'),
            'operator': recharge_data.get('operator', '未知'),
            'recharge_time': recharge_data.get('created_at', ''),
            'current_balance': recharge_data.get('current_balance', 0),
            'reprint_reason': reprint_reason,
            'reprint_time': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        
        return render_template('recharge_receipt_reprint.html', **template_data)
        
    except Exception as e:
        return f"生成补打印页面失败: {str(e)}", 500

@recharge_bp.route('/api/recharge/<int:recharge_id>/reprint', methods=['POST'])
@login_required
def reprint_recharge_receipt(recharge_id):
    """补打印充值小票"""
    try:
        staff_name = session.get('staff_name', '')
        data = request.get_json() or {}
        reprint_reason = data.get('reprint_reason', '客户要求补打印')
        
        # 检查充值记录是否存在
        recharge_record = RechargeRecord.query.get(recharge_id)
        if not recharge_record:
            return jsonify({
                'success': False,
                'error': '充值记录不存在'
            }), 404
        
        # 检查权限（可以查看充值记录的员工可以补打印）
        from utils import check_area_permission
        permission_check = check_area_permission(staff_name, recharge_record.customer_id)
        if not permission_check['has_permission']:
            return jsonify({
                'success': False,
                'error': permission_check['message']
            }), 403
        
        # 创建补打印记录
        reprint_record = ReceiptReprint(
            record_type='recharge',
            record_id=recharge_id,
            customer_id=recharge_record.customer_id,
            operator=staff_name,
            reprint_reason=reprint_reason
        )
        db.session.add(reprint_record)
        db.session.commit()
        
        # 记录操作日志
        from utils import log_recharge_refund_operation
        log_recharge_refund_operation(
            reprint_record.id,
            'recharge_receipt_reprint',
            staff_name,
            {
                'recharge_id': recharge_id,
                'customer_id': recharge_record.customer_id,
                'reason': reprint_reason
            }
        )
        
        return jsonify({
            'success': True,
            'reprint_id': reprint_record.id,
            'message': '补打印记录已创建，请检查打印机'
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': f'补打印充值小票失败: {str(e)}'
        }), 500

@recharge_bp.route('/api/recharge/refund/<int:refund_id>/reprint', methods=['POST'])
@login_required
def reprint_refund_receipt(refund_id):
    """补打印退充小票"""
    try:
        staff_name = session.get('staff_name', '')
        data = request.get_json() or {}
        reprint_reason = data.get('reprint_reason', '客户要求补打印')
        
        # 检查退充记录是否存在
        refund_record = RechargeRefund.query.get(refund_id)
        if not refund_record:
            return jsonify({
                'success': False,
                'error': '退充记录不存在'
            }), 404
        
        # 检查权限
        from utils import check_area_permission
        permission_check = check_area_permission(staff_name, refund_record.customer_id)
        if not permission_check['has_permission']:
            return jsonify({
                'success': False,
                'error': permission_check['message']
            }), 403
        
        # 创建补打印记录
        reprint_record = ReceiptReprint(
            record_type='refund',
            record_id=refund_id,
            customer_id=refund_record.customer_id,
            operator=staff_name,
            reprint_reason=reprint_reason
        )
        db.session.add(reprint_record)
        db.session.commit()
        
        # 记录操作日志
        from utils import log_recharge_refund_operation
        log_recharge_refund_operation(
            reprint_record.id,
            'refund_receipt_reprint',
            staff_name,
            {
                'refund_id': refund_id,
                'customer_id': refund_record.customer_id,
                'reason': reprint_reason
            }
        )
        
        return jsonify({
            'success': True,
            'reprint_id': reprint_record.id,
            'message': '补打印记录已创建，请检查打印机'
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': f'补打印退充小票失败: {str(e)}'
        }), 500

@recharge_bp.route('/api/receipts/reprints', methods=['GET'])
@login_required
def get_reprint_records():
    """获取补打印记录列表"""
    try:
        staff_role = session.get('staff_role', '')
        staff_name = session.get('staff_name', '')
        staff_area = session.get('staff_area', '')
        
        # 获取查询参数
        record_type = request.args.get('record_type')  # 'recharge' or 'refund'
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        customer_phone = request.args.get('customer_phone')
        operator = request.args.get('operator')
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        
        # 默认查询近7天数据
        if not start_date:
            start_date = (datetime.datetime.now() - datetime.timedelta(days=7)).strftime('%Y-%m-%d')
        if not end_date:
            end_date = datetime.datetime.now().strftime('%Y-%m-%d')
        
        # 转换为datetime对象
        start_datetime = datetime.datetime.strptime(f"{start_date} 00:00:00", '%Y-%m-%d %H:%M:%S')
        end_datetime = datetime.datetime.strptime(f"{end_date} 23:59:59", '%Y-%m-%d %H:%M:%S')
        
        # 构建查询
        reprint_query = ReceiptReprint.query.filter(
            ReceiptReprint.created_at.between(start_datetime, end_datetime)
        )
        
        # 应用筛选条件
        if record_type:
            reprint_query = reprint_query.filter_by(record_type=record_type)
        
        if operator:
            reprint_query = reprint_query.filter_by(operator=operator)
        
        if customer_phone:
            customer = Customer.query.filter_by(phone=customer_phone).first()
            if customer:
                reprint_query = reprint_query.filter_by(customer_id=customer.id)
            else:
                reprint_query = reprint_query.filter_by(id=-1)
        
        # 应用权限过滤
        if staff_role != 'manager':
            reprint_query = reprint_query.filter_by(operator=staff_name)
        elif staff_role == 'manager' and staff_area and staff_area != '总部':
            area_staff = Staff.query.filter_by(area=staff_area).all()
            area_staff_names = [user.name for user in area_staff]
            reprint_query = reprint_query.filter(ReceiptReprint.operator.in_(area_staff_names))
        
        # 按创建时间降序排序
        reprint_query = reprint_query.order_by(ReceiptReprint.created_at.desc())
        
        # 分页
        paginated_reprints = reprint_query.paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        # 构建结果数据
        reprint_details = []
        for reprint in paginated_reprints.items:
            # 获取客户信息
            customer = Customer.query.get(reprint.customer_id)
            customer_name = customer.name if customer else '未知'
            customer_phone = customer.phone if customer else '未知'
            
            # 获取原记录信息
            original_record_info = {}
            if reprint.record_type == 'recharge':
                original_record = RechargeRecord.query.get(reprint.record_id)
                if original_record:
                    original_record_info = {
                        'amount': float(original_record.amount),
                        'gift_amount': float(original_record.gift_amount or 0),
                        'payment_method': original_record.payment_method,
                        'created_at': original_record.created_at.isoformat()
                    }
            elif reprint.record_type == 'refund':
                original_record = RechargeRefund.query.get(reprint.record_id)
                if original_record:
                    original_record_info = {
                        'refund_amount': float(original_record.refund_amount),
                        'refund_gift_amount': float(original_record.refund_gift_amount or 0),
                        'refund_reason': original_record.refund_reason,
                        'created_at': original_record.created_at.isoformat()
                    }
            
            reprint_details.append({
                'id': reprint.id,
                'record_type': reprint.record_type,
                'record_id': reprint.record_id,
                'customer_name': customer_name,
                'customer_phone': customer_phone,
                'operator': reprint.operator,
                'reprint_reason': reprint.reprint_reason or '',
                'created_at': reprint.created_at.isoformat(),
                'original_record': original_record_info
            })
        
        return jsonify({
            'success': True,
            'reprints': reprint_details,
            'current_page': page,
            'total_pages': paginated_reprints.pages,
            'per_page': per_page,
            'total': paginated_reprints.total
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'获取补打印记录失败: {str(e)}'
        }), 500