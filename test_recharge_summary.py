#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试充值汇总统计是否正确处理退充记录
"""

from models import db, RechargeRecord, RechargeRefund
from app import create_app

def test_recharge_summary():
    """测试充值汇总统计"""
    app = create_app()
    with app.app_context():
        print("=== 充值汇总统计测试 ===\n")
        
        # 获取所有充值记录
        recharge_records = RechargeRecord.query.all()
        print(f"总充值记录数: {len(recharge_records)}")
        
        # 计算原始充值总金额
        original_total = sum(record.amount for record in recharge_records)
        print(f"原始充值总金额: ¥{original_total:.2f}")
        
        # 计算实际充值金额（扣除退充）
        actual_total = 0
        refund_total = 0
        
        for record in recharge_records:
            # 计算该充值记录的退充金额
            refunds = RechargeRefund.query.filter_by(
                recharge_record_id=record.id,
                status='completed'
            ).all()
            refund_amount = sum(refund.refund_amount for refund in refunds)
            actual_recharge = record.amount - refund_amount
            
            actual_total += actual_recharge
            refund_total += refund_amount
            
            if refund_amount > 0:
                print(f"充值记录ID {record.id}: 原金额 ¥{record.amount:.2f}, 退充 ¥{refund_amount:.2f}, 实际 ¥{actual_recharge:.2f}")
        
        print(f"\n总退充金额: ¥{refund_total:.2f}")
        print(f"实际充值总金额: ¥{actual_total:.2f}")
        print(f"差额: ¥{original_total - actual_total:.2f}")
        
        # 测试按营业员统计
        print("\n=== 按营业员统计测试 ===")
        recharge_summary = {}
        
        for record in recharge_records:
            # 计算该充值记录的退充金额和赠送金额扣除
            refunds = RechargeRefund.query.filter_by(
                recharge_record_id=record.id,
                status='completed'
            ).all()
            refund_amount = sum(refund.refund_amount for refund in refunds)
            
            # 计算按比例扣除的赠送金额
            gift_deduction = 0.0
            if record.amount > 0 and record.gift_amount > 0:
                for refund in refunds:
                    gift_ratio = refund.refund_amount / record.amount
                    gift_deduction += (record.gift_amount * gift_ratio)
            
            actual_recharge = record.amount - refund_amount
            actual_gift = (record.gift_amount or 0) - gift_deduction
            
            operator = record.operator or '未知'
            if operator not in recharge_summary:
                recharge_summary[operator] = {
                    'count': 0,
                    'recharge_amount': 0.0,
                    'gift_amount': 0.0,
                    'total_amount': 0.0
                }

            # 只统计未完全退充的记录
            if actual_recharge > 0:
                recharge_summary[operator]['count'] += 1
                recharge_summary[operator]['recharge_amount'] += actual_recharge
                recharge_summary[operator]['gift_amount'] += actual_gift
                recharge_summary[operator]['total_amount'] += actual_recharge + actual_gift
        
        # 显示统计结果
        for operator, stats in recharge_summary.items():
            if stats['total_amount'] > 0:
                print(f"{operator}: 笔数={stats['count']}, 充值=¥{stats['recharge_amount']:.2f}, 赠送=¥{stats['gift_amount']:.2f}, 总计=¥{stats['total_amount']:.2f}")

if __name__ == '__main__':
    test_recharge_summary()
