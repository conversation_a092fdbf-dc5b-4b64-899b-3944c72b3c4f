from flask import Flask, render_template, request, redirect, url_for, jsonify, current_app, session, flash
import os
import json
import datetime
import uuid
import pymysql
from werkzeug.security import generate_password_hash, check_password_hash
from functools import wraps
from models import db, Customer, Order, Clothing, ClothingPhoto, RechargeRecord, Staff, init_db
from models import MallCustomer, MallProductDiscount, MallMonthlyBill, MallDiscountHistory, Product, OrderStatusLog
from models import RechargeGiftRule, MemberServiceDiscount, RefundRecord
from config import config
from utils import (
    save_base64_image,
    generate_order_number,
    update_customer_balance,
    generate_barcode_base64,
    calculate_gift_amount,
    process_refund,
    is_mobile_request,
)
from io import BytesIO
import base64
from PIL import Image, ImageDraw, ImageFont
import re
# 新增：导入蓝图注册函数与通用登录装饰器
from blueprints.auth import login_required
from blueprints import register_blueprints
from urllib.parse import urlencode  # 新增：构造带查询参数的URL

# 将PyMySQL注册为MySQLdb
pymysql.install_as_MySQLdb()

def get_service_price_from_requirements(special_reqs, service_name, item_total_price, total_services_count):
    """
    从special_requirements中提取指定服务的价格

    Args:
        special_reqs (dict): 特殊要求字典
        service_name (str): 服务名称（如'精洗', '织补', '改衣', '其他'）
        item_total_price (float): 衣物总价格（用于向后兼容）
        total_services_count (int): 该衣物包含的服务总数（用于平均分配）

    Returns:
        float: 该服务的价格
    """
    # 服务名称映射（处理新旧版本的差异）
    service_mapping = {
        '精洗': ['fineWashPrice', 'washPrice', 'wash'],
        '奢洗': ['luxuryWashPrice', 'wash'],
        '洗衣': ['washPrice', 'fineWashPrice', 'wash'],
        '织补': ['darnPrice', 'darn'],
        '改衣': ['alterPrice', 'alter'],
        '其他': ['otherPrice', 'other']
    }

    # 尝试从special_requirements中获取具体价格
    if service_name in service_mapping:
        for price_key in service_mapping[service_name]:
            if price_key in special_reqs:
                price_value = special_reqs[price_key]
                # 处理不同的数据格式
                if isinstance(price_value, dict) and 'price' in price_value:
                    return float(price_value['price'])
                elif isinstance(price_value, (int, float)):
                    return float(price_value)

    # 如果无法从special_requirements获取价格，使用平均分配策略
    if item_total_price > 0 and total_services_count > 0:
        return item_total_price / total_services_count

    # 最后的后备方案：返回默认价格
    default_prices = {
        '精洗': 25.0,
        '奢洗': 40.0,
        '洗衣': 25.0,
        '织补': 20.0,
        '改衣': 30.0,
        '其他': 20.0
    }

    return default_prices.get(service_name, 20.0)

def create_app(config_name='default'):
    """应用工厂函数 - 创建并配置Flask应用实例

    参数:
        config_name (str): 配置名称，默认为'default'

    返回:
        Flask: 配置好的Flask应用实例
    """
    app = Flask(__name__)

    # =====================================================================
    # 应用配置
    # =====================================================================
    # 加载配置
    app.config.from_object(config[config_name])
    config[config_name].init_app(app)

    # 确保有密钥用于会话
    if not app.config.get('SECRET_KEY'):
        app.config['SECRET_KEY'] = 'laundry_system_secret_key_for_session'

    # 设置会话过期时间为12小时
    app.config['PERMANENT_SESSION_LIFETIME'] = datetime.timedelta(hours=12)

    # 更新数据库配置为在线数据库
    app.config['SQLALCHEMY_DATABASE_URI'] = 'mysql+pymysql://ytgf:ZEzaJikm2kNakFbk@49.232.0.106:3306/ytgf'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

    # 初始化数据库
    init_db(app)

    # =====================================================================
    # 注册各业务蓝图
    # =====================================================================
    register_blueprints(app)

    # =====================================================================
    # 辅助函数和装饰器
    # =====================================================================
    # login_required装饰器已从blueprints.auth模块导入

    # =====================================================================
    # 认证相关路由
    # =====================================================================
    @app.route('/')
    @login_required
    def index():
        """主页

        返回应用的主页面

        返回:
            渲染后的主页模板
        """
        staff_name = session.get('staff_name', '未登录')
        return render_template('index.html', staff_name=staff_name)

    @app.route('/mobile')
    @login_required
    def index_mobile():
        """移动端主页

        返回移动端布局的主页面，与桌面端功能完全一致，仅样式调整
        """
        staff_name = session.get('staff_name', '未登录')
        return render_template('index_mobile.html', staff_name=staff_name)

    @app.route('/rack')
    @login_required
    def rack_redirect():
        """格架管理重定向
        
        根据设备类型跳转到对应的格架管理页面
        """
        is_mobile = is_mobile_request(request)
        if is_mobile:
            return redirect('/rack_management?m=1')
        else:
            return redirect('/rack_management')

    # 打印相关功能已迁移到 blueprints/print.py


    # =====================================================================
    # 订单处理相关路由
    # =====================================================================

    # =====================================================================
    # 订单查询和历史相关路由
    # =====================================================================

    @app.route('/data_summary')
    @login_required
    def data_summary():
        """数据汇总页面

        返回数据汇总页面

        返回:
            渲染后的数据汇总页面模板
        """
        template_name = 'data_summary_mobile.html' if request.args.get('m') == '1' else 'data_summary.html'
        return render_template(template_name)

    # =====================================================================
    # 数据汇总相关路由
    # =====================================================================
    @app.route('/api/summary_data')
    @login_required
    def get_summary_data():
        """获取汇总数据API

        根据日期范围查询订单和充值数据，生成汇总统计信息

        参数:
            start_date (str, 可选): 开始日期，格式为YYYY-MM-DD，默认为7天前
            end_date (str, 可选): 结束日期，格式为YYYY-MM-DD，默认为当天

        返回:
            JSON: 包含汇总数据的JSON对象
        """
        try:
            # 获取当前登录用户的角色和信息
            staff_role = session.get('staff_role', '')
            staff_name = session.get('staff_name', '')
            staff_area = session.get('staff_area', '')

            print(f"数据汇总权限检查 - 角色: {staff_role}, 姓名: {staff_name}, 区域: {staff_area}")

            # 获取查询参数
            start_date = request.args.get('start_date')
            end_date = request.args.get('end_date')
            selected_operator = request.args.get('operator')  # 新增：营业员筛选参数

            # 默认查询近7天数据
            if not start_date:
                start_date = (datetime.datetime.now() - datetime.timedelta(days=7)).strftime('%Y-%m-%d')
            if not end_date:
                end_date = datetime.datetime.now().strftime('%Y-%m-%d')

            # 转换为datetime对象
            start_datetime = datetime.datetime.strptime(f"{start_date} 00:00:00", '%Y-%m-%d %H:%M:%S')
            end_datetime = datetime.datetime.strptime(f"{end_date} 23:59:59", '%Y-%m-%d %H:%M:%S')

            # 优化：使用预加载避免N+1查询问题
            from sqlalchemy.orm import joinedload, selectinload

            # 构建订单查询，查询已付款和部分退款的订单（按支付时间筛选），排除余额付款
            orders_query = Order.query.options(
                joinedload(Order.customer),  # 预加载客户信息
                selectinload(Order.clothes),  # 预加载衣物信息
                selectinload(Order.refund_records)  # 预加载退款记录
            ).filter(
                Order.payment_time.between(start_datetime, end_datetime),
                Order.payment_status.in_(['已付款', '部分退款']),
                Order.payment_method != '余额'
            )

            # 应用营业员筛选（在权限过滤之前）
            if selected_operator:
                orders_query = orders_query.filter_by(operator=selected_operator)
                print(f"营业员筛选：只查看营业员 {selected_operator} 的订单")

            # 应用权限过滤
            if staff_role != 'manager':
                # 普通营业员只能查看自己操作的订单
                if not selected_operator:  # 如果没有指定营业员，默认查看自己的
                    orders_query = orders_query.filter_by(operator=staff_name)
                elif selected_operator != staff_name:  # 如果指定了其他营业员，无权限查看
                    orders_query = orders_query.filter(Order.id == -1)  # 返回空结果
                print(f"普通营业员权限：只查看操作员为 {staff_name} 的订单")
            elif staff_role == 'manager' and staff_area and staff_area != '总部':
                # 区域管理员只能查看自己区域的订单
                area_staff = Staff.query.filter_by(area=staff_area).all()
                area_staff_names = [user.name for user in area_staff]
                if not selected_operator:  # 如果没有指定营业员，查看区域内所有营业员
                    orders_query = orders_query.filter(Order.operator.in_(area_staff_names))
                elif selected_operator not in area_staff_names:  # 如果指定的营业员不在区域内，无权限查看
                    orders_query = orders_query.filter(Order.id == -1)  # 返回空结果
                print(f"区域管理员权限：查看区域 {staff_area} 的订单，包含营业员: {area_staff_names}")
            else:
                # 超级管理员或总部管理员可以查看所有订单（营业员筛选已在上面处理）
                print(f"超级管理员权限：查看所有订单")

            orders = orders_query.all()

            # 查询充值记录（同样应用权限过滤）
            recharge_query = RechargeRecord.query.filter(
                RechargeRecord.created_at.between(start_datetime, end_datetime)
            )

            # 对充值记录也应用相同的权限过滤
            if staff_role != 'manager':
                # 普通营业员只能查看自己操作的充值记录
                recharge_query = recharge_query.filter_by(operator=staff_name)
            elif staff_role == 'manager' and staff_area and staff_area != '总部':
                # 区域管理员只能查看自己区域的充值记录
                area_staff = Staff.query.filter_by(area=staff_area).all()
                area_staff_names = [user.name for user in area_staff]
                recharge_query = recharge_query.filter(RechargeRecord.operator.in_(area_staff_names))

            recharge_records = recharge_query.all()

            # 优化：预计算所有需要的数据，避免在循环中重复查询
            # 计算实际收入（扣除退款金额）
            total_actual_revenue = 0
            for order in orders:
                # 由于使用了 selectinload，refund_records 已经预加载，不会产生额外查询
                refund_amount = sum(r.refund_amount for r in order.refund_records)
                # 实际收入 = 订单金额 - 退款金额
                actual_revenue = order.total_amount - refund_amount
                total_actual_revenue += actual_revenue

            # 汇总数据
            summary = {
                'total_orders': len(orders),
                'total_revenue': total_actual_revenue,  # 使用实际收入
                'payment_methods': {},
                'order_status': {},
                'daily_data': {},
                'service_types': {},  # 格式: {service: {'items': 件数, 'amount': 金额}}
                'total_items': 0,  # 总衣物件数
                'total_recharge': sum(record.amount for record in recharge_records),  # 充值总金额
                'recharge_by_method': {},  # 按支付方式统计充值金额
                'operator_stats': {}  # 按营业员统计数据
            }

            # 统计支付方式
            for order in orders:
                # 计算该订单的实际收入（扣除退款）
                refund_amount = sum(r.refund_amount for r in order.refund_records)
                actual_revenue = order.total_amount - refund_amount

                # 支付方式统计
                if order.payment_method in summary['payment_methods']:
                    summary['payment_methods'][order.payment_method] += 1
                else:
                    summary['payment_methods'][order.payment_method] = 1

                # 如果是现金支付，计入现金收入（使用实际收入）
                if order.payment_method == '现金':
                    if 'cash_revenue' not in summary:
                        summary['cash_revenue'] = 0
                    summary['cash_revenue'] += actual_revenue

                # 订单状态统计
                if order.status in summary['order_status']:
                    summary['order_status'][order.status] += 1
                else:
                    summary['order_status'][order.status] = 1

                # 营业员统计（使用实际收入）
                operator = order.operator or '未知'
                if operator not in summary['operator_stats']:
                    summary['operator_stats'][operator] = {
                        'orders': 0,
                        'revenue': 0,
                        'items': 0
                    }
                summary['operator_stats'][operator]['orders'] += 1
                summary['operator_stats'][operator]['revenue'] += actual_revenue

                # 日期统计（使用实际收入）
                date_str = order.payment_time.strftime('%Y-%m-%d')
                if date_str not in summary['daily_data']:
                    summary['daily_data'][date_str] = {
                        'orders': 0,
                        'revenue': 0,
                        'items': 0,
                        'cash_revenue': 0
                    }
                summary['daily_data'][date_str]['orders'] += 1
                summary['daily_data'][date_str]['revenue'] += actual_revenue

                # 如果是现金收入，添加到每日现金收入（使用实际收入）
                if order.payment_method == '现金':
                    summary['daily_data'][date_str]['cash_revenue'] += actual_revenue

            # 统计服务类型和衣物类型
            # 从付款订单中查询衣物
            order_ids = [order.id for order in orders]
            clothes = Clothing.query.filter(Clothing.order_id.in_(order_ids)).all()

            # 衣物总件数 - 修复：使用数量总和而不是条目数
            summary['total_items'] = sum(item.quantity or 1 for item in clothes)

            for item in clothes:
                # 获取衣物数量
                item_quantity = item.quantity or 1

                # 添加到每日衣物件数 - 修复：使用实际数量
                order = item.order
                date_str = order.payment_time.strftime('%Y-%m-%d')
                if date_str in summary['daily_data']:
                    summary['daily_data'][date_str]['items'] += item_quantity

                # 获取订单对应的营业员，更新衣物件数 - 修复：使用实际数量
                order = item.order
                operator = order.operator or '未知'
                if operator in summary['operator_stats']:
                    summary['operator_stats'][operator]['items'] += item_quantity

                # 服务类型统计
                services = []
                if item.services:
                    try:
                        services = json.loads(item.services)
                    except:
                        services = []

                # 获取该衣物的总金额（price字段已经是总金额，不是单价）
                item_total_amount = item.price or 0

                # 对于每个服务，累加数量和按比例分配的金额
                if services:  # 只有当存在服务时才分配金额
                    service_count = len(services)
                    amount_per_service = item_total_amount / service_count  # 平均分配金额

                    for service in services:
                        if service not in summary['service_types']:
                            summary['service_types'][service] = {'items': 0, 'amount': 0.0}

                        # 累加件数
                        summary['service_types'][service]['items'] += item_quantity

                        # 累加平均分配的金额
                        summary['service_types'][service]['amount'] += amount_per_service

            # 按支付方式统计充值
            for record in recharge_records:
                method = record.payment_method
                if method in summary['recharge_by_method']:
                    summary['recharge_by_method'][method] += record.amount
                else:
                    summary['recharge_by_method'][method] = record.amount

            # 按营业员统计充值汇总
            recharge_summary = {}
            for record in recharge_records:
                operator = record.operator or '未知'
                if operator not in recharge_summary:
                    recharge_summary[operator] = {
                        'count': 0,
                        'recharge_amount': 0.0,
                        'gift_amount': 0.0,
                        'total_amount': 0.0
                    }

                recharge_summary[operator]['count'] += 1
                recharge_summary[operator]['recharge_amount'] += record.amount
                recharge_summary[operator]['gift_amount'] += (record.gift_amount or 0)
                recharge_summary[operator]['total_amount'] += record.amount + (record.gift_amount or 0)

            summary['recharge_summary'] = recharge_summary

            # 验证服务类型金额总和
            service_total_amount = sum(service_data['amount'] for service_data in summary['service_types'].values())
            print(f"数据汇总验证 - 订单总收入: {summary['total_revenue']:.2f}, 服务类型金额总和: {service_total_amount:.2f}")

            # 排序数据
            summary['daily_data'] = dict(sorted(summary['daily_data'].items()))
            summary['payment_methods'] = dict(sorted(summary['payment_methods'].items(), key=lambda x: x[1], reverse=True))
            summary['order_status'] = dict(sorted(summary['order_status'].items()))
            summary['service_types'] = dict(sorted(summary['service_types'].items(), key=lambda x: x[1]['items'], reverse=True))
            summary['recharge_by_method'] = dict(sorted(summary['recharge_by_method'].items(), key=lambda x: x[1], reverse=True))

            # 对营业员数据按订单数降序排序
            summary['operator_stats'] = dict(sorted(summary['operator_stats'].items(), key=lambda x: x[1]['orders'], reverse=True))

            return jsonify({
                'success': True,
                'data': summary,
                'query_period': {
                    'start_date': start_date,
                    'end_date': end_date
                }
            })

        except Exception as e:
            print(f"获取汇总数据出错: {str(e)}")
            import traceback
            traceback.print_exc()
            return jsonify({'error': str(e)}), 500

    @app.route('/api/operators')
    @login_required
    def get_operators():
        """获取营业员列表API

        根据当前用户权限返回可查看的营业员列表

        返回:
            JSON: 包含营业员列表的JSON对象
        """
        try:
            # 获取当前登录用户的角色和信息
            staff_role = session.get('staff_role', '')
            staff_name = session.get('staff_name', '')
            staff_area = session.get('staff_area', '')

            operators = []

            if staff_role != 'manager':
                # 普通营业员只能查看自己
                operators = [staff_name]
            elif staff_role == 'manager' and staff_area and staff_area != '总部':
                # 区域管理员只能查看自己区域的营业员
                area_staff = Staff.query.filter_by(area=staff_area).all()
                operators = [user.name for user in area_staff]
            else:
                # 超级管理员或总部管理员可以查看所有营业员
                # 从订单中获取所有营业员（去重）
                all_operators = db.session.query(Order.operator).filter(
                    Order.operator.isnot(None),
                    Order.operator != ''
                ).distinct().all()
                operators = [op[0] for op in all_operators if op[0]]

            # 排序营业员列表
            operators = sorted(list(set(operators)))

            return jsonify({
                'success': True,
                'operators': operators
            })

        except Exception as e:
            print(f"获取营业员列表出错: {str(e)}")
            return jsonify({'error': str(e)}), 500

    @app.route('/api/export_recharge_summary', methods=['GET'])
    @login_required
    def export_recharge_summary():
        """导出充值汇总Excel"""
        try:
            # 获取当前登录用户的角色和信息
            staff_role = session.get('staff_role', '')
            staff_name = session.get('staff_name', '')
            staff_area = session.get('staff_area', '')

            # 获取查询参数
            start_date = request.args.get('start_date')
            end_date = request.args.get('end_date')
            selected_operator = request.args.get('operator')

            # 默认查询近7天数据
            if not start_date:
                start_date = (datetime.datetime.now() - datetime.timedelta(days=7)).strftime('%Y-%m-%d')
            if not end_date:
                end_date = datetime.datetime.now().strftime('%Y-%m-%d')

            # 转换为datetime对象
            start_datetime = datetime.datetime.strptime(f"{start_date} 00:00:00", '%Y-%m-%d %H:%M:%S')
            end_datetime = datetime.datetime.strptime(f"{end_date} 23:59:59", '%Y-%m-%d %H:%M:%S')

            # 构建充值记录查询
            recharge_query = RechargeRecord.query.filter(
                RechargeRecord.created_at.between(start_datetime, end_datetime)
            )

            # 应用营业员筛选（在权限过滤之前）
            if selected_operator:
                recharge_query = recharge_query.filter_by(operator=selected_operator)

            # 应用权限过滤
            if staff_role != 'manager':
                if not selected_operator:
                    recharge_query = recharge_query.filter_by(operator=staff_name)
                elif selected_operator != staff_name:
                    recharge_query = recharge_query.filter(RechargeRecord.id == -1)
            elif staff_role == 'manager' and staff_area and staff_area != '总部':
                area_staff = Staff.query.filter_by(area=staff_area).all()
                area_staff_names = [user.name for user in area_staff]
                if not selected_operator:
                    recharge_query = recharge_query.filter(RechargeRecord.operator.in_(area_staff_names))
                elif selected_operator not in area_staff_names:
                    recharge_query = recharge_query.filter(RechargeRecord.id == -1)

            records = recharge_query.all()

            # 按营业员汇总数据
            recharge_summary = {}
            for record in records:
                operator = record.operator or '未知'
                if operator not in recharge_summary:
                    recharge_summary[operator] = {
                        'count': 0,
                        'recharge_amount': 0.0,
                        'gift_amount': 0.0,
                        'total_amount': 0.0
                    }

                recharge_summary[operator]['count'] += 1
                recharge_summary[operator]['recharge_amount'] += record.amount
                recharge_summary[operator]['gift_amount'] += (record.gift_amount or 0)
                recharge_summary[operator]['total_amount'] += record.amount + (record.gift_amount or 0)

            # 构建Excel数据
            import io
            from flask import Response
            import openpyxl
            from openpyxl.styles import Font, Alignment

            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = "充值汇总"

            # 设置表头
            headers = ['排名', '营业员姓名', '充值笔数', '充值金额(元)', '赠送金额(元)', '总金额(元)', '占比(%)']
            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=1, column=col, value=header)
                cell.font = Font(bold=True)
                cell.alignment = Alignment(horizontal='center')

            # 计算总金额用于占比
            total_amount = sum(stats['total_amount'] for stats in recharge_summary.values())

            # 按总金额降序排序并写入数据
            sorted_operators = sorted(recharge_summary.items(), key=lambda x: x[1]['total_amount'], reverse=True)

            for row, (operator, stats) in enumerate(sorted_operators, 2):
                percentage = (stats['total_amount'] / total_amount * 100) if total_amount > 0 else 0

                ws.cell(row=row, column=1, value=row-1)  # 排名
                ws.cell(row=row, column=2, value=operator)
                ws.cell(row=row, column=3, value=stats['count'])
                ws.cell(row=row, column=4, value=stats['recharge_amount'])
                ws.cell(row=row, column=5, value=stats['gift_amount'])
                ws.cell(row=row, column=6, value=stats['total_amount'])
                ws.cell(row=row, column=7, value=f"{percentage:.2f}%")

            # 调整列宽
            for column in ws.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                ws.column_dimensions[column_letter].width = adjusted_width

            # 保存到内存
            output = io.BytesIO()
            wb.save(output)
            output.seek(0)

            return Response(
                output.getvalue(),
                mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                headers={
                    'Content-Disposition': f'attachment; filename=充值汇总_{start_date}_{end_date}.xlsx'
                }
            )

        except Exception as e:
            print(f"导出充值汇总失败: {str(e)}")
            return jsonify({'error': str(e)}), 500

    # 充值明细相关功能已迁移到 blueprints/recharge.py

    # =====================================================================
    # 订单详情和打印相关路由
    # =====================================================================


    @app.route('/update_clothing_item', methods=['POST'])
    @login_required
    def update_clothing_item():
        """更新衣物信息

        更新订单中的衣物信息，包括名称、颜色、价格、备注等

        请求体:
            JSON对象，包含衣物ID、订单ID、名称、价格等信息

        返回:
            JSON: 包含更新结果的JSON对象
        """
        try:
            # 获取请求数据
            data = request.json
            if not data:
                return jsonify({'success': False, 'error': '无效的请求数据'}), 400

            # 验证必要字段
            required_fields = ['id', 'order_id', 'name', 'price']
            for field in required_fields:
                if field not in data:
                    return jsonify({'success': False, 'error': f'缺少必要字段: {field}'}), 400

            # 查找衣物记录
            clothing = Clothing.query.get(data['id'])
            if not clothing:
                return jsonify({'success': False, 'error': '未找到衣物记录'}), 404

            # 检查衣物是否属于指定订单
            if clothing.order_id != data['order_id']:
                return jsonify({'success': False, 'error': '衣物不属于指定订单'}), 403

            # 获取订单信息
            order = Order.query.get(clothing.order_id)
            if not order:
                return jsonify({'success': False, 'error': '未找到关联订单'}), 404

            # 计算价格差异以更新订单总金额
            price_difference = float(data['price']) - clothing.price

            # 更新衣物基本信息
            clothing.name = data['name']
            clothing.color = data.get('color', '')
            clothing.price = float(data['price'])
            clothing.remarks = data.get('remarks', '')
            clothing.flaw = data.get('flaw', '')  # 添加瑕疵字段支持

            # 更新服务类型
            clothing.services = json.dumps(data.get('services', []))

            # 更新特殊要求
            if 'requirements' in data:
                clothing.special_requirements = json.dumps(data['requirements'])

            # 更新订单总金额
            order.total_amount = float(order.total_amount) + price_difference

            # 处理图片更新
            if 'photos' in data:
                # 删除旧的照片记录
                old_photos = ClothingPhoto.query.filter_by(clothing_id=clothing.id).all()
                for old_photo in old_photos:
                    # 删除文件
                    try:
                        old_file_path = os.path.join(app.static_folder, old_photo.image_path)
                        if os.path.exists(old_file_path):
                            os.remove(old_file_path)
                    except Exception as e:
                        print(f"删除旧照片文件失败: {e}")
                    # 删除数据库记录
                    db.session.delete(old_photo)

                # 保存新的照片
                photos = data.get('photos', [])
                customer_phone = order.customer.phone if order.customer else 'unknown'
                for j, photo_data in enumerate(photos):
                    if photo_data:  # 确保照片数据不为空
                        # 保存图片
                        image_path = save_base64_image(photo_data, customer_phone, j)
                        if image_path:
                            # 创建照片记录
                            photo = ClothingPhoto(
                                clothing_id=clothing.id,
                                image_path=image_path
                            )
                            db.session.add(photo)

            # 更新订单修改标记
            order.is_modified = True
            order.last_modified_at = datetime.datetime.now()
            order.last_modified_by = session.get('staff_name', '未知')
            order.modification_count = (order.modification_count or 0) + 1

            # 保存更改
            db.session.commit()

            return jsonify({
                'success': True,
                'message': '衣物信息更新成功',
                'new_price': clothing.price,
                'new_total': order.total_amount
            })

        except Exception as e:
            db.session.rollback()
            print(f"更新衣物信息错误: {e}")
            return jsonify({'success': False, 'error': f'更新衣物信息失败: {str(e)}'}), 500

    @app.route('/reset_admin', methods=['GET'])
    def reset_admin():
        """重置管理员账号（仅开发环境使用）"""
        if app.config['ENV'] != 'development':
            return jsonify({'error': '此功能仅在开发环境可用'}), 403

        try:
            # 尝试查找管理员账号
            admin = Staff.query.filter_by(username="admin").first()

            if admin:
                # 更新现有账号
                admin.password = generate_password_hash("admin123")
                admin.name = "系统管理员"
                admin.role = "manager"
                admin.is_active = True
                db.session.commit()
                return jsonify({
                    'success': True,
                    'message': '管理员账号已重置',
                    'username': 'admin',
                    'password': 'admin123'
                })
            else:
                # 创建新账号
                new_admin = Staff(
                    username="admin",
                    password=generate_password_hash("admin123"),
                    name="系统管理员",
                    role="manager",
                    area="总部",
                    is_active=True
                )
                db.session.add(new_admin)
                db.session.commit()
                return jsonify({
                    'success': True,
                    'message': '管理员账号已创建',
                    'username': 'admin',
                    'password': 'admin123'
                })
        except Exception as e:
            db.session.rollback()
            return jsonify({'error': f'重置管理员账号失败: {str(e)}'}), 500

    # =====================================================================
    # 用户管理相关路由
    # =====================================================================
    def validate_status_change(old_status, new_status):
        """状态流转校验已取消，所有变更直接放行"""
        return True

    @app.route('/static/js/clothing-options.js')
    def clothing_options():
        """服装选项脚本"""
        return app.send_static_file('js/clothing-options.js')


    # 添加订单状态处理页面路由
    # =====================================================================
    # 退款相关路由
    # =====================================================================
    @app.route('/api/orders/<int:order_id>/refund', methods=['POST'])
    @login_required
    def refund_order(order_id):
        """处理订单退款"""
        try:
            data = request.json
            refund_amount = float(data.get('refund_amount', 0))
            refund_reason = data.get('refund_reason', '')
            # 根据支付方式智能确定默认退款方式，若前端未显式指定则自动选择最合理的方式
            refund_method = data.get('refund_method')

            if not refund_method:
                if order := Order.query.get(order_id):
                    pay_method = order.payment_method or ''
                    if pay_method == '余额':
                        refund_method = '余额退回'
                    elif pay_method == '现金':
                        refund_method = '现金退款'
                    else:
                        # 如微信、支付宝等线上支付，默认原路退回
                        refund_method = '原路退回'
                else:
                    # 若订单不存在（稍后会有404处理），为了类型安全先赋默认值
                    refund_method = '原路退回'

            # 验证输入
            if refund_amount <= 0:
                return jsonify({'success': False, 'error': '退款金额必须大于0'}), 400

            if not refund_reason.strip():
                return jsonify({'success': False, 'error': '请填写退款原因'}), 400

            # 查找订单
            order = Order.query.get_or_404(order_id)

            # 权限检查：只有管理员或订单操作员可以退款
            staff_role = session.get('staff_role')
            staff_name = session.get('staff_name')
            if staff_role != 'manager' and order.operator != staff_name:
                return jsonify({'success': False, 'error': '您没有权限对此订单进行退款操作'}), 403

            # 处理退款
            result = process_refund(
                order=order,
                refund_amount=refund_amount,
                refund_reason=refund_reason,
                operator=staff_name,
                refund_method=refund_method
            )

            if result['success']:
                return jsonify(result)
            else:
                return jsonify(result), 400

        except ValueError:
            return jsonify({'success': False, 'error': '退款金额格式错误'}), 400
        except Exception as e:
            return jsonify({'success': False, 'error': str(e)}), 500

    @app.route('/api/orders/<int:order_id>/refund_records', methods=['GET'])
    @login_required
    def get_order_refund_records(order_id):
        """获取订单的退款记录"""
        try:
            order = Order.query.get_or_404(order_id)

            # 查询退款记录
            refund_records = RefundRecord.query.filter_by(order_id=order_id).order_by(RefundRecord.created_at.desc()).all()

            records_data = []
            for record in refund_records:
                records_data.append({
                    'id': record.id,
                    'refund_amount': record.refund_amount,
                    'original_amount': record.original_amount,
                    'refund_method': record.refund_method,
                    'refund_reason': record.refund_reason,
                    'refund_type': record.refund_type,
                    'status': record.status,
                    'operator': record.operator,
                    'created_at': record.created_at.isoformat(),
                    'processed_at': record.processed_at.isoformat() if record.processed_at else None,
                    'remarks': record.remarks
                })

            return jsonify({
                'order_id': order_id,
                'order_number': order.order_number,
                'refund_records': records_data
            })

        except Exception as e:
            return jsonify({'error': str(e)}), 500

    @app.route('/api/refund_records', methods=['GET'])
    @login_required
    def get_refund_records():
        """获取退款记录列表（支持分页和筛选）"""
        try:
            page = int(request.args.get('page', 1))
            per_page = int(request.args.get('per_page', 10))
            order_number = request.args.get('order_number', '').strip()
            operator = request.args.get('operator', '').strip()
            start_date = request.args.get('start_date', '').strip()
            end_date = request.args.get('end_date', '').strip()

            # 构建查询
            query = RefundRecord.query

            # 权限控制：非管理员只能查看自己的退款记录
            staff_role = session.get('staff_role')
            staff_name = session.get('staff_name')
            if staff_role != 'manager':
                query = query.filter(RefundRecord.operator == staff_name)

            # 筛选条件
            if order_number:
                query = query.filter(RefundRecord.order_number.like(f'%{order_number}%'))

            if operator and staff_role == 'manager':  # 只有管理员可以按操作员筛选
                query = query.filter(RefundRecord.operator.like(f'%{operator}%'))

            if start_date:
                query = query.filter(RefundRecord.created_at >= start_date)

            if end_date:
                query = query.filter(RefundRecord.created_at <= end_date + ' 23:59:59')

            # 分页查询
            pagination = query.order_by(RefundRecord.created_at.desc()).paginate(
                page=page, per_page=per_page, error_out=False
            )

            records_data = []
            for record in pagination.items:
                records_data.append({
                    'id': record.id,
                    'order_number': record.order_number,
                    'customer_name': record.customer.name if record.customer else '未知',
                    'refund_amount': record.refund_amount,
                    'original_amount': record.original_amount,
                    'refund_method': record.refund_method,
                    'refund_reason': record.refund_reason,
                    'refund_type': record.refund_type,
                    'status': record.status,
                    'operator': record.operator,
                    'created_at': record.created_at.isoformat(),
                    'processed_at': record.processed_at.isoformat() if record.processed_at else None
                })

            return jsonify({
                'records': records_data,
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total': pagination.total,
                    'pages': pagination.pages,
                    'has_prev': pagination.has_prev,
                    'has_next': pagination.has_next
                }
            })

        except Exception as e:
            return jsonify({'error': str(e)}), 500

    # ================================================================
    # 移动端自动跳转处理
    # ================================================================

    @app.before_request
    def _auto_mobile_redirect():
        """检测User-Agent，自动跳转到移动端模板或路由。

        逻辑说明：
        1. 如果已经包含 m=1 参数，或访问的是 /mobile 路由，则直接放行；
        2. 对 /static 与 /api 前缀的请求不做任何处理；
        3. 若检测到移动端 UA：
           • 首页(/) 重定向至 /mobile，以使用专门的移动端首页；
           • 其它页面在保持原路径的同时追加 "m=1" 查询参数，实现模板切换。
        """
        # 仅对普通 GET 页面请求做移动端重定向，避免影响 POST/PUT 等带表单或 JSON 的请求
        # 1) 若请求方法不是 GET（如登录、下单等接口通常为 POST）直接放行；
        # 2) 若前端明确说明自己想要 JSON（Accept: application/json）或通过 AJAX 调用（X-Requested-With: XMLHttpRequest），直接放行；
        if request.method != 'GET':
            return

        if request.headers.get('Accept', '').startswith('application/json') or \
           request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return

        # 跳过静态文件和 API 接口
        if request.path.startswith('/static') or request.path.startswith('/api'):
            return

        # 已指定移动端参数或已在专门移动首页
        if request.args.get('m') == '1' or request.path.startswith('/mobile'):
            return

        # 非移动端访问，忽略
        if not is_mobile_request(request):
            return

        # 针对首页做专门跳转
        if request.path == '/':
            return redirect(url_for('index_mobile'))

        # 其它页面追加 m=1 参数
        args = request.args.to_dict(flat=True)
        args['m'] = '1'
        return redirect(f"{request.path}?{urlencode(args)}")

    # =====================================================================


    @app.route('/recharge/management')
    @login_required
    def recharge_management():
        """充值记录管理页面"""
        return render_template('recharge_management.html')

    @app.route('/refund/records')
    @login_required
    def refund_records():
        """退充记录管理页面"""
        return render_template('refund_records.html')

    return app

if __name__ == '__main__':
    app = create_app('development')
    # 设置环境变量
    app.config['ENV'] = 'development'
    app.run(host='0.0.0.0', port=5000, debug=True)